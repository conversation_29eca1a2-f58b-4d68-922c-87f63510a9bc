#!/usr/bin/env python
# encoding: utf-8

import os
import sys
import re
from subprocess import run, CalledProcessError, PIP<PERSON>

def extract_weibo_id(input_str):
    """
    Extract Weibo ID from either a full URL or just the ID.
    Args:
        input_str: Either a full Weibo URL or just the ID
    Returns:
        str: The extracted Weibo ID
    """
    # If input is already just an ID, return it
    if not '/' in input_str and not 'weibo.com' in input_str:
        return input_str
    
    # Extract ID from URL using regex
    # https://weibo.com/1678105910/PxpyZeJqL
    if not 'weibo.com' in input_str:
        return None
    
    url_pattern = r'/(\w+)\??[^/]*$'
    match = re.search(url_pattern, input_str.rstrip('/'))
    if match:
        return match.group(1)
    raise ValueError("Invalid Weibo URL or ID format")

def crawl_weibo(url_or_id, only_tweet: bool = False):
    """
    Crawl both tweet and its reposts for a given URL or ID.
    Args:
        url_or_id: Either a Weibo URL or just the ID
    """
    original_dir = os.getcwd()
    try:
        # Extract the Weibo ID
        weibo_id = extract_weibo_id(url_or_id)
        
        # First crawl the tweet itself
        print(f"Crawling tweet {weibo_id}...")
        os.chdir(os.path.join('WeiboSpider', 'weibospider'))
        result = run(['python', 'run_spider.py', 'tweet_by_tweet_id', f'--tweet_id {weibo_id}'], check=True, stderr=PIPE, text=True)
        os.chdir(original_dir)
        
        # Then crawl the reposts
        print(f"Crawling reposts for {weibo_id}...")
        os.chdir(os.path.join('WeiboSpider', 'weibospider'))
        result = run(['python', 'run_spider.py', 'repost', f'--tweet_id {weibo_id}'], check=True, stderr=PIPE, text=True)
        os.chdir(original_dir)

        # Then crawl the comments
        print(f"Crawling comments for {weibo_id}...")
        os.chdir(os.path.join('WeiboSpider', 'weibospider'))
        result = run(['python', 'run_spider.py', 'comment', f'--tweet_id {weibo_id}'], check=True, stderr=PIPE, text=True)
        os.chdir(original_dir)

    except CalledProcessError as e:
        print(f"Crawling failed with error code {e.returncode}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
    except Exception as e:
        print(f"Error occurred: {str(e)}")
    finally:
        # Always change back to original directory
        os.chdir(original_dir)

def main():
    if len(sys.argv) != 2:
        print("Usage: python crawl_repost.py <weibo_url_or_id>")
        sys.exit(1)
    
    url_or_id = sys.argv[1]
    crawl_weibo(url_or_id)

if __name__ == "__main__":
    main()