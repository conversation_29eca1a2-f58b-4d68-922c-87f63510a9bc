from flask import Flask, jsonify, request, send_from_directory
from crawl_predict import crawl_and_predict
import mysql.connector
from datetime import datetime
import traceback

import cron_crawl

app = Flask(__name__)

# MySQL Configuration
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': '123456',
    'database': 'cas',
    'charset': 'utf8mb4',
    'collation': 'utf8mb4_unicode_ci'
}

def get_db_connection():
    return mysql.connector.connect(**db_config)

@app.route('/')
def home():
    return send_from_directory('ui', 'index.html')

@app.route('/styles/<path:filename>')
def serve_styles(filename):
    return send_from_directory('ui/styles', filename)

@app.route('/js/<path:filename>')
def serve_js(filename):
    return send_from_directory('ui/js', filename)

@app.route('/api/predict')
def predict():
    tweet_id = request.args.get('tweet_id').strip()
    refresh = request.args.get('refresh') == 'true'
    if not tweet_id:
        return jsonify({
            'status': 'error',
            'error': 'Missing tweet_id parameter'
        }), 400
    
    try:
        result = crawl_and_predict(tweet_id, refresh)
        return jsonify({
            'status': 'success',
            'data': result
        }), 200
    except Exception as e:
        detailed_error_traceback = traceback.format_exc()
        print("\n--- Detailed Server Error ---")
        print(detailed_error_traceback)
        print("---------------------------\n")

        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/api/history', methods=['GET'])
def history():
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        cursor.execute("SELECT * FROM tweet_data ORDER BY pred_time DESC")
        results = cursor.fetchall()
        
        # Convert datetime objects to string for JSON serialization
        for row in results:
            row['created_at'] = row['created_at'].isoformat()
            row['crawl_time'] = row['crawl_time'].isoformat()
        
        cursor.close()
        conn.close()
        
        return jsonify({
            'status': 'success',
            'data': results
        }), 200
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/api/history', methods=['PUT'])
def put_history():
    try:
        data = request.get_json()
        print(f'put_history get data: {data}')
        if not data or 'tweet_id' not in data:
            return jsonify({
                'status': 'error',
                'error': 'Missing tweet_id in request body'
            }), 400

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if tweet_id exists
        cursor.execute("SELECT tweet_id FROM tweet_data WHERE tweet_id = %s", (data['tweet_id'],))
        exists = cursor.fetchone()

        # Get column names from the table
        cursor.execute("SHOW COLUMNS FROM tweet_data")
        columns = [column['Field'] for column in cursor.fetchall()]
        
        # Filter data to only include valid column names
        filtered_data = {k: v for k, v in data.items() if k in columns}
        
        if exists:
            # Build dynamic UPDATE query
            set_clause = ", ".join([f"{key} = %s" for key in filtered_data.keys()])
            update_query = f"UPDATE tweet_data SET {set_clause} WHERE tweet_id = %s"
            
            # Prepare values for the query
            values = list(filtered_data.values())
            values.append(data['tweet_id'])  # Add tweet_id for WHERE clause
            
            print(f'update_query: {update_query}')
            print(f'values: {values}')
            cursor.execute(update_query, values)
        else:
            # Build dynamic INSERT query
            columns_str = ", ".join(filtered_data.keys())
            placeholders = ", ".join(["%s"] * len(filtered_data))
            insert_query = f"INSERT INTO tweet_data ({columns_str}) VALUES ({placeholders})"
            # Prepare values for the query
            values = list(filtered_data.values())
             
            print(f'insert_query: {insert_query}')
            print(f'values: {values}')
            cursor.execute(insert_query, values)

        conn.commit()
        cursor.close()
        conn.close()

        return jsonify({
            'status': 'success',
            'message': 'Record updated' if exists else 'Record created'
        }), 200

    except Exception as e:
        print(f'Error: {e}')
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

if __name__ == '__main__':
    # Start cron job before starting the Flask app
    print("Starting cron job...")
    cron_thread = cron_crawl.cron_crawl()

    try:
        # Start Flask app
        print("Starting Flask app...")
        app.run(host='localhost', port=8080, debug=True)
    except KeyboardInterrupt:
        print("\nShutting down...")
    finally:
        # The cron thread will automatically exit because it's a daemon thread
        print(f"App stopped. Cron job status: {'running' if cron_thread.is_alive() else 'stopped'}")
        print("Cron job will exit automatically.")