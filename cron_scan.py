import os
from subprocess import run, CalledProcessError
import json
import datetime

def cron_once():
    # 一次性的定时任务

    user_ids = ['2656274875']
    crawl_by_user_ids(user_ids)
    print(get_tasks())



def get_tasks():
    file_dir = 'WeiboSpider/output/tweet_by_user_id_tweet.jsonl'
    tasks = []
    with open(file_dir, 'r') as f:
        for line in f:
            data = json.loads(line)
            tweet_id = data['mblogid']
            created_at = data['created_at']
            timestamp = int(datetime.datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S').timestamp())
            tasks.append({'tweet_id': tweet_id, 'created_at': timestamp})
    return tasks

    

def crawl_by_user_ids(user_ids):
    original_dir = os.getcwd()

    os.chdir(os.path.join('WeiboSpider', 'weibospider'))
    user_ids_str = ' '.join(user_ids)
    try:
        result = run(['python', 'run_spider.py', 'tweet_by_user_id', f'--tweet_user_ids {user_ids_str}'], check=True, text=True)
    except CalledProcessError as e:
        print(f"Command failed with return code {e.returncode}")
        print(f"Command: {' '.join(e.cmd)}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        if e.stdout:
            print(f"Standard output: {e.stdout}")
        raise  # Re-raise the exception
    finally:
        os.chdir(original_dir)


if __name__ == '__main__':
    cron_once()